"""
Queue job handlers.

This package contains handlers for queue jobs.
"""

from typing import Callable, Dict, Union  # noqa: F401

from app.services.queue.handlers.generic_job import HANDLERS as GENERIC_JOB_HANDLERS
from app.services.queue.handlers.job_tracking import HAN<PERSON><PERSON><PERSON> as JOB_TRACKING_HANDLERS
from app.services.queue.handlers.submission_processing import (
    HANDLERS as SUBMISSION_PROCESSING_HANDLERS,
)
from app.services.queue.worker_interface import JobHandlerInterface

# Create handler registry
HANDLERS: Dict[str, Union[JobHandlerInterface, Callable]] = {
    # Function-based handlers
    **JOB_TRACKING_HANDLERS,
    **GENERIC_JOB_HANDLERS,
    **SUBMISSION_PROCESSING_HANDLERS,
}


# Add job tracking handlers
HANDLERS.update(JOB_TRACKING_HANDLERS)

# Add generic job handlers
HANDLERS.update(GENERIC_JOB_HANDLERS)

__all__ = ["HANDLERS"]
