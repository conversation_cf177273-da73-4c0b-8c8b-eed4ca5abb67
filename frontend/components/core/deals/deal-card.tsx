"use client"

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Deal, DealStatus } from '@/lib/types/deal';
import { MoreHorizontal, ExternalLink, MapPin } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
}

// Utility functions for minimal, premium design
const getStatusColor = (status: DealStatus) => {
  switch (status) {
    case DealStatus.NEW:
      return 'bg-blue-50 text-blue-700';
    case DealStatus.TRIAGE:
      return 'bg-yellow-50 text-yellow-700';
    case DealStatus.REVIEWED:
      return 'bg-purple-50 text-purple-700';
    case DealStatus.APPROVED:
      return 'bg-green-50 text-green-700';
    case DealStatus.NEGOTIATING:
      return 'bg-orange-50 text-orange-700';
    case DealStatus.CLOSED:
      return 'bg-gray-50 text-gray-700';
    case DealStatus.EXCLUDED:
    case DealStatus.REJECTED:
      return 'bg-red-50 text-red-700';
    default:
      return 'bg-gray-50 text-gray-700';
  }
};

const getStageColor = (stage: string) => {
  const normalizedStage = stage?.toUpperCase() || '';
  const stageColors: Record<string, string> = {
    'PRE SEED': 'bg-blue-50 text-blue-700',
    'SEED': 'bg-green-50 text-green-700',
    'SERIES A': 'bg-purple-50 text-purple-700',
    'SERIES B': 'bg-orange-50 text-orange-700',
    'SERIES C': 'bg-red-50 text-red-700',
    'GROWTH': 'bg-indigo-50 text-indigo-700',
  };
  return stageColors[normalizedStage] || 'bg-gray-50 text-gray-700';
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500 text-white',
    'bg-green-500 text-white',
    'bg-purple-500 text-white',
    'bg-orange-500 text-white',
    'bg-pink-500 text-white',
    'bg-indigo-500 text-white',
  ];

  const hash = name.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

const getScoreColor = (score: number) => {
  if (score >= 80) return 'text-green-600';
  if (score >= 60) return 'text-blue-600';
  if (score >= 40) return 'text-orange-600';
  return 'text-red-600';
};

export function DealCard({ deal, index, onClick }: DealCardProps) {
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        delay: index * 0.05
      }
    }
  };

  // Extract and process data with backend-aligned fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = Array.isArray(deal.sector)
    ? deal.sector
    : deal.sector ? [deal.sector] : [];
  const stage = deal.stage || '';
  const website = deal.company_website;
  const score = deal.scoring?.total_score;
  const status = deal.status;

  // Generate company initials for avatar
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  const avatarColor = getAvatarColor(companyName);

  // Create summary from available data (1-2 lines max)
  const createSummary = () => {
    const parts = [];
    if (sectors.length > 0) {
      parts.push(`${sectors[0]} company`);
    }
    if (stage) {
      parts.push(`at ${stage} stage`);
    }
    if (parts.length === 0) {
      return 'Investment opportunity';
    }
    return parts.join(' ') + '.';
  };

  const summary = createSummary();

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group"
    >
      <Card className="relative overflow-hidden border-0 shadow-sm hover:shadow-md transition-all duration-300 bg-white rounded-2xl cursor-pointer">
        <Link href={`/deals/${deal.id}`} className="block">
          <CardContent className="p-6">
            {/* Header with Avatar and Company Info */}
            <div className="flex items-start gap-4 mb-4">
              <Avatar className="h-12 w-12 flex-shrink-0">
                <AvatarFallback className={cn("text-sm font-semibold", avatarColor)}>
                  {initials}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <h3 className="text-lg font-bold text-gray-900 leading-tight group-hover:text-blue-600 transition-colors">
                    {companyName}
                  </h3>

                  {/* Actions Menu - Only visible on hover */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => e.preventDefault()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>Move to...</DropdownMenuItem>
                      <DropdownMenuItem>Flag</DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Stage and Sector Chips */}
                <div className="flex flex-wrap gap-2 mt-2">
                  {stage && (
                    <Badge
                      variant="outline"
                      className={cn("text-xs font-medium rounded-full px-2 py-1", getStageColor(stage))}
                    >
                      {stage.toUpperCase()}
                    </Badge>
                  )}
                  {sectors.slice(0, 1).map((sector, idx) => (
                    <Badge
                      key={idx}
                      variant="outline"
                      className="text-xs font-medium bg-gray-50 text-gray-700 border-gray-200 rounded-full px-2 py-1"
                    >
                      {sector.toUpperCase()}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Summary */}
            <p className="text-sm text-gray-600 leading-relaxed mb-4 line-clamp-2">
              {summary}
            </p>

            {/* Bottom Row - Score, Website, Location */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {score && (
                  <Badge variant="outline" className="text-xs font-medium bg-blue-50 text-blue-700 border-blue-200 rounded-full px-2 py-1">
                    {score}
                  </Badge>
                )}
                {website && (
                  <Badge variant="outline" className="text-xs text-gray-600 bg-gray-50 border-gray-200 rounded-full px-2 py-1 flex items-center gap-1">
                    <ExternalLink className="h-3 w-3" />
                    Website
                  </Badge>
                )}
              </div>

              {/* Location placeholder - would come from enriched_data in real implementation */}
              <div className="flex items-center gap-1 text-xs text-gray-500">
                <MapPin className="h-3 w-3" />
                <span>—</span>
              </div>
            </div>
          </CardContent>
        </Link>
      </Card>
    </motion.div>
  );
}
