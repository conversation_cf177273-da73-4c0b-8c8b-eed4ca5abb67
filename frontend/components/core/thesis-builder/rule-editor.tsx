"use client"

import React, { useState, useEffect, useRef } from 'react';
import { X, Save, AlertCircle, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  ScoringRule,
  RuleType,
  ConditionOperator,
  AggregationType,
  FilterCondition,
  getOperatorDisplay,
  getAggregationDisplay
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { RuleSummary } from './rule-summary';

interface RuleEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: Partial<ScoringRule>) => void;
  rule?: Partial<ScoringRule>;
  questionId?: string;
  questionType?: QuestionType;
  questionLabel?: string;
  isRepeatable?: boolean;
  sectionId?: string;
  allowBonusRules?: boolean; // New prop to control bonus rule creation
  questionOptions?: Array<{ label: string; value: string }>; // Question options for MCQ/Boolean
}

// Get valid operators for a question type
function getValidOperators(questionType: QuestionType): ConditionOperator[] {
  switch (questionType) {
    case QuestionType.SHORT_TEXT:
    case QuestionType.LONG_TEXT:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.CONTAINS,
        ConditionOperator.NOT_CONTAINS,
        ConditionOperator.STARTS_WITH,
        ConditionOperator.ENDS_WITH
      ];
    case QuestionType.NUMBER:
    case QuestionType.RANGE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    case QuestionType.BOOLEAN:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
    case QuestionType.SINGLE_SELECT:
    case QuestionType.MULTI_SELECT:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.IN,
        ConditionOperator.NOT_IN,
        ConditionOperator.CONTAINS,
        ConditionOperator.NOT_CONTAINS
      ];
    case QuestionType.DATE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    default:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
  }
}

// Tags Picker Component for MCQ/Boolean question options
interface TagsPickerProps {
  selectedValues: string[];
  onValuesChange: (values: string[]) => void;
  options: Array<{ label: string; value: string }>;
  placeholder?: string;
  disabled?: boolean;
  allowMultiple?: boolean;
}

function TagsPicker({ 
  selectedValues, 
  onValuesChange, 
  options, 
  placeholder = "Select options...", 
  disabled = false,
  allowMultiple = true 
}: TagsPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = React.useMemo(() => {
    if (!searchTerm) return options;
    return options.filter(option =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [options, searchTerm]);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleOption = (value: string) => {
    if (allowMultiple) {
      const newValues = selectedValues.includes(value)
        ? selectedValues.filter(v => v !== value)
        : [...selectedValues, value];
      onValuesChange(newValues);
    } else {
      // Single select mode
      onValuesChange(selectedValues.includes(value) ? [] : [value]);
    }
  };

  const removeValue = (value: string) => {
    onValuesChange(selectedValues.filter(v => v !== value));
  };

  const getOptionLabel = (value: string) => {
    const option = options.find(opt => opt.value === value);
    return option?.label || value;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Selected Tags Display */}
      <div
        className={cn(
          "min-h-[32px] w-full rounded-md border border-input bg-background px-3 py-1 text-sm ring-offset-background",
          "flex flex-wrap gap-1 items-center cursor-pointer",
          disabled && "opacity-50 cursor-not-allowed",
          isOpen && "ring-2 ring-ring ring-offset-2"
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        {selectedValues.length > 0 ? (
          selectedValues.map((value) => (
            <Badge
              key={value}
              variant="secondary"
              className="text-xs h-6 px-2 gap-1"
            >
              {getOptionLabel(value)}
              {!disabled && (
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeValue(value);
                  }}
                />
              )}
            </Badge>
          ))
        ) : (
          <span className="text-muted-foreground">{placeholder}</span>
        )}
        <ChevronDown className={cn("h-4 w-4 ml-auto transition-transform", isOpen && "rotate-180")} />
      </div>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && !disabled && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.15 }}
            className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-md max-h-60 overflow-hidden"
          >
            {/* Search Input */}
            <div className="p-2 border-b">
              <Input
                ref={inputRef}
                placeholder="Search options..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-8"
                autoFocus
              />
            </div>

            {/* Options List */}
            <div className="max-h-48 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((option) => {
                  const isSelected = selectedValues.includes(option.value);
                  return (
                    <div
                      key={option.value}
                      className={cn(
                        "px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                        "flex items-center justify-between",
                        isSelected && "bg-accent text-accent-foreground"
                      )}
                      onClick={() => toggleOption(option.value)}
                    >
                      <span>{option.label}</span>
                      {isSelected && (
                        <Badge variant="outline" className="text-xs">
                          Selected
                        </Badge>
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="px-3 py-2 text-sm text-muted-foreground">
                  No options found
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export function RuleEditor({
  isOpen,
  onClose,
  onSave,
  rule,
  questionId,
  questionType = QuestionType.SHORT_TEXT,
  questionLabel,
  isRepeatable = false,
  sectionId,
  allowBonusRules = true, // Default to true for backward compatibility
  questionOptions = [] // Question options for MCQ/Boolean questions
}: RuleEditorProps) {
  const [formData, setFormData] = useState<Partial<ScoringRule>>({
    rule_type: RuleType.SCORING,
    weight: 1.0,
    is_deleted: false,
    question_id: questionId || '', // Ensure question_id is set
    // REQUIRED: Always include condition field (backend validation requires this)
    condition: {
      question_id: questionId || '',
      operator: ConditionOperator.EQUALS,
      value: ''
    } as FilterCondition,
    ...rule
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);

  // Reset form when rule changes
  useEffect(() => {
    if (rule) {
      setFormData({
        ...rule,
        // Ensure question_id is set for the rule
        question_id: questionId || rule.question_id,
        // Ensure condition has the correct question_id - handle both FilterCondition and CompoundFilter
        condition: rule.condition ? (() => {
          // Check if it's a FilterCondition (has operator and value)
          if ('operator' in rule.condition && 'value' in rule.condition) {
            return {
              ...rule.condition,
              question_id: questionId || rule.question_id || (rule.condition as FilterCondition).question_id
            } as FilterCondition;
          } else {
            // It's a CompoundFilter, return as-is
            return rule.condition;
          }
        })() : {
          question_id: questionId || rule.question_id || '',
          operator: ConditionOperator.EQUALS,
          value: ''
        } as FilterCondition
      });
    } else {
      setFormData({
        rule_type: RuleType.SCORING,
        weight: 1.0,
        question_id: questionId || '',
        is_deleted: false,
        condition: {
          question_id: questionId || '',
          operator: ConditionOperator.EQUALS,
          value: ''
        } as FilterCondition
      });
    }
  }, [rule, questionId]);

  // Validate form
  useEffect(() => {
    const newErrors: string[] = [];
    const newWarnings: string[] = [];

    // CRITICAL: Validate condition field (required by backend for ALL rules)
    if (!formData.condition) {
      newErrors.push('Condition is required for all rules');
    } else if ('question_id' in formData.condition) {
      // Simple condition validation
      if (!formData.condition.question_id) {
        newErrors.push('Question ID is required in condition');
      }
      if (!formData.condition.operator) {
        newErrors.push('Operator is required in condition');
      }
      if (formData.condition.value === undefined || formData.condition.value === '') {
        newErrors.push('Condition value is required');
      }
    }

    if (formData.rule_type === RuleType.SCORING) {
      if (!formData.question_id) {
        newErrors.push('Question ID is required for scoring rules');
      }
      if (!formData.weight || formData.weight <= 0) {
        newErrors.push('Weight must be positive');
      }
    }

    if (formData.rule_type === RuleType.BONUS) {
      if (!formData.bonus_points || formData.bonus_points <= 0) {
        newErrors.push('Bonus points must be positive');
      }
    }

    if (isRepeatable && formData.aggregation) {
      if (formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE) {
        if (!formData.value_field) {
          newErrors.push('Value field is required for this aggregation type');
        }
      }
      if (!formData.value_field) {
        newWarnings.push('Value field is recommended for aggregation');
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
  }, [formData, isRepeatable]);

  const handleSave = () => {
    if (errors.length === 0) {
      // Ensure we have a clean payload that matches backend expectations
      const cleanedData: Partial<ScoringRule> = {
        rule_type: formData.rule_type || RuleType.SCORING,
        question_id: formData.question_id || questionId,
        weight: formData.weight || 1.0,
        condition: formData.condition,
        bonus_points: formData.bonus_points,
        aggregation: formData.aggregation,
        value_field: formData.value_field,
        notes: formData.notes,
        good_answers: formData.good_answers,
        bad_answers: formData.bad_answers,
        is_deleted: false
      };

      // Remove undefined values to avoid backend validation issues
      Object.keys(cleanedData).forEach(key => {
        if (cleanedData[key as keyof typeof cleanedData] === undefined) {
          delete cleanedData[key as keyof typeof cleanedData];
        }
      });

      console.log('🚨 URGENT DEBUG - RuleEditor handleSave:');
      console.log('  questionId prop:', questionId);
      console.log('  formData.question_id:', formData.question_id);
      console.log('  final question_id:', cleanedData.question_id);
      console.log('  condition:', cleanedData.condition);
      
      // Type-safe condition value check (FilterCondition has value, CompoundFilter doesn't)
      const condition = cleanedData.condition as FilterCondition;
      if (condition && 'value' in condition) {
        console.log('  condition.value type:', typeof condition.value);
        console.log('  condition.value:', condition.value);
      }
      
      console.log('  Full cleaned data:', cleanedData);
      
      onSave(cleanedData);
    }
  };

  const validOperators = getValidOperators(questionType);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {rule ? 'Edit Scoring Rule' : 'Create Scoring Rule'}
          </DialogTitle>
          <DialogDescription>
            Configure how this question will be scored in your thesis.
          </DialogDescription>
          {questionLabel && (
            <div className="mt-2 p-2 bg-muted rounded text-sm">
              <strong>Question:</strong> {questionLabel}
            </div>
          )}
        </DialogHeader>

        <div className="space-y-4">
          {/* Rule Type */}
          <div className="space-y-2">
            <Label>Rule Type</Label>
            <Select
              value={formData.rule_type}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                rule_type: value as RuleType
              }))}
              disabled={!allowBonusRules && formData.rule_type === RuleType.SCORING}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={RuleType.SCORING}>Scoring Rule</SelectItem>
                {allowBonusRules && (
                  <SelectItem value={RuleType.BONUS}>Bonus Rule</SelectItem>
                )}
              </SelectContent>
            </Select>
            {!allowBonusRules && (
              <p className="text-xs text-muted-foreground">
                Bonus rules can only be created in the Bonus tab
              </p>
            )}
          </div>

          {/* Weight (for scoring rules) */}
          {formData.rule_type === RuleType.SCORING && (
            <div className="space-y-2">
              <Label>Weight</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.weight || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  weight: e.target.value ? Number(e.target.value) : 1
                }))}
                placeholder="1.0"
              />
            </div>
          )}

          {/* Bonus Points (for bonus rules) */}
          {formData.rule_type === RuleType.BONUS && (
            <div className="space-y-2">
              <Label>Bonus Points</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.bonus_points || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  bonus_points: e.target.value ? Number(e.target.value) : 0
                }))}
                placeholder="2.0"
              />
            </div>
          )}

          {/* Condition Operator */}
          <div className="space-y-2">
            <Label>Operator</Label>
            <Select
              value={formData.condition && 'operator' in formData.condition ? formData.condition.operator : ''}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                condition: {
                  question_id: questionId || prev.question_id || '',
                  operator: value as ConditionOperator,
                  value: (prev.condition as FilterCondition)?.value || ''
                }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                {validOperators.map(op => (
                  <SelectItem key={op} value={op}>
                    {getOperatorDisplay(op)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Condition Value */}
          <div className="space-y-2">
            <Label>Expected Value</Label>
            {(questionType === QuestionType.SINGLE_SELECT || 
              questionType === QuestionType.MULTI_SELECT || 
              questionType === QuestionType.BOOLEAN) && questionOptions.length > 0 ? (
              <TagsPicker
                selectedValues={(() => {
                  const currentValue = formData.condition && 'value' in formData.condition ? formData.condition.value : '';
                  if (Array.isArray(currentValue)) {
                    return currentValue;
                  }
                  if (typeof currentValue === 'string' && currentValue) {
                    // Try to parse as array first, then split by comma, or use as single value
                    try {
                      const parsed = JSON.parse(currentValue);
                      return Array.isArray(parsed) ? parsed : [currentValue];
                    } catch {
                      return currentValue.includes(',') ? currentValue.split(',').map(v => v.trim()) : [currentValue];
                    }
                  }
                  return [];
                })()}
                onValuesChange={(values) => setFormData(prev => ({
                  ...prev,
                  condition: {
                    question_id: questionId || prev.question_id || '',
                    operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                    value: values.length === 1 ? values[0] : values // Store array directly for multiple values
                  }
                }))}
                options={questionOptions}
                placeholder={`Select ${questionType === QuestionType.BOOLEAN ? 'true or false' : 'options'}...`}
                allowMultiple={true} // Always allow multiple selection for better flexibility
              />
            ) : (
              <Input
                value={formData.condition && 'value' in formData.condition ? formData.condition.value : ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  condition: {
                    question_id: questionId || prev.question_id || '',
                    operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                    value: e.target.value
                  }
                }))}
                placeholder="Enter expected value"
              />
            )}
          </div>

          {/* Text Answer Scoring (for text questions) */}
          {(questionType === QuestionType.SHORT_TEXT || questionType === QuestionType.LONG_TEXT) && (
            <>
              <div className="space-y-2">
                <Label>Good Answers (Optional)</Label>
                <Textarea
                  value={formData.good_answers || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    good_answers: e.target.value || undefined
                  }))}
                  placeholder="Examples of good answers that should score highly (one per line)&#10;e.g.:&#10;Strong technical team with relevant experience&#10;Clear path to market penetration&#10;Solid revenue model"
                  className="min-h-[80px]"
                />
                <p className="text-xs text-muted-foreground">
                  Define examples of high-quality answers. The LLM will use these to score similar responses highly.
                </p>
              </div>

              <div className="space-y-2">
                <Label>Bad Answers (Optional)</Label>
                <Textarea
                  value={formData.bad_answers || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    bad_answers: e.target.value || undefined
                  }))}
                  placeholder="Examples of poor answers that should score low (one per line)&#10;e.g.:&#10;Vague or generic responses&#10;No clear business model&#10;Unrealistic projections"
                  className="min-h-[80px]"
                />
                <p className="text-xs text-muted-foreground">
                  Define examples of poor answers. The LLM will use these to score similar responses poorly.
                </p>
              </div>
            </>
          )}

          {/* Aggregation (for repeatable sections) */}
          {isRepeatable && (
            <>
              <div className="space-y-2">
                <Label>Aggregation Type</Label>
                <Select
                  value={formData.aggregation || ''}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    aggregation: value as AggregationType
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select aggregation" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AggregationType).map(type => (
                      <SelectItem key={type} value={type}>
                        {getAggregationDisplay(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>
                  Value Field 
                  {formData.aggregation && formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE 
                    ? ' (required)' 
                    : ' (optional)'
                  }
                </Label>
                <Input
                  value={formData.value_field || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    value_field: e.target.value || undefined
                  }))}
                  placeholder="Field name for aggregation"
                />
              </div>
            </>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label>Notes (optional)</Label>
            <Textarea
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                notes: e.target.value || undefined
              }))}
              placeholder="Additional notes about this rule"
              className="min-h-[60px]"
            />
          </div>

          {/* Rule Summary */}
          <div className="space-y-2">
            <Label>Rule Summary</Label>
            <div className="p-3 bg-muted rounded-md">
              <RuleSummary rule={formData} questionLabel={questionLabel} />
            </div>
          </div>

          {/* Validation Messages */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {warnings.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={errors.length > 0}>
            <Save className="h-4 w-4 mr-2" />
            Save Rule
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
