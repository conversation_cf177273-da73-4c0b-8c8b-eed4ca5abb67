"use client"

import React from 'react';
import { 
  ScoringRule, 
  RuleType, 
  FilterCondition,
  CompoundFilter,
  getOperatorDisplay,
  getAggregationDisplay,
  LogicalOperator 
} from '@/lib/types/thesis';

interface RuleSummaryProps {
  rule: Partial<ScoringRule>;
  questionLabel?: string;
}

function formatConditionValue(value: any): string {
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  if (typeof value === 'string' && value.startsWith('[') && value.endsWith(']')) {
    // Try to parse JSON string array
    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.join(', ');
      }
    } catch {
      // If parsing fails, treat as regular string
    }
  }
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  if (value === null || value === undefined || value === '') {
    return '[not set]';
  }
  return String(value);
}

function summarizeFilterCondition(condition: FilterCondition, questionLabel?: string): string {
  const label = questionLabel || `Question ${condition.question_id}`;
  const operator = getOperatorDisplay(condition.operator);
  const value = formatConditionValue(condition.value);
  
  return `${label} ${operator} ${value}`;
}

function summarizeCompoundCondition(condition: CompoundFilter): string {
  if (!condition.conditions || condition.conditions.length === 0) {
    return '[no conditions]';
  }

  const conditionSummaries = condition.conditions.map(cond => {
    if ('question_id' in cond) {
      return summarizeFilterCondition(cond);
    } else {
      return `(${summarizeCompoundCondition(cond)})`;
    }
  });

  const operator = condition.operator === LogicalOperator.AND ? ' AND ' : 
                   condition.operator === LogicalOperator.OR ? ' OR ' : 
                   ' NOT ';

  if (condition.operator === LogicalOperator.NOT && conditionSummaries.length === 1) {
    return `NOT ${conditionSummaries[0]}`;
  }

  return conditionSummaries.join(operator);
}

export function RuleSummary({ rule, questionLabel }: RuleSummaryProps) {
  if (!rule.rule_type) {
    return <span className="text-muted-foreground italic">Incomplete rule configuration</span>;
  }

  // Handle scoring rules
  if (rule.rule_type === RuleType.SCORING) {
    if (!rule.condition) {
      return <span className="text-muted-foreground italic">No condition specified</span>;
    }

    let conditionSummary: string;
    if ('question_id' in rule.condition) {
      conditionSummary = summarizeFilterCondition(rule.condition, questionLabel);
    } else {
      conditionSummary = summarizeCompoundCondition(rule.condition);
    }

    const weight = rule.weight || 1;
    let summary = `Award ${weight} point${weight !== 1 ? 's' : ''} if ${conditionSummary}`;

    // Add aggregation info for repeatable sections
    if (rule.aggregation && rule.section_id) {
      const aggregationType = getAggregationDisplay(rule.aggregation);
      summary += ` (${aggregationType}`;
      
      if (rule.value_field) {
        summary += ` of ${rule.value_field}`;
      }
      
      if (rule.aggregate_operator && rule.aggregate_threshold) {
        summary += ` ${getOperatorDisplay(rule.aggregate_operator)} ${rule.aggregate_threshold}`;
      }
      
      summary += ')';
    }

    // Add text scoring info if defined
    const hasTextScoring = rule.good_answers || rule.bad_answers;
    if (hasTextScoring) {
      summary += ' • LLM scoring enabled';
    }

    return (
      <span>
        {summary}
        {hasTextScoring && (
          <div className="text-xs text-muted-foreground mt-1 space-y-1">
            {rule.good_answers && (
              <div>✓ Good examples defined</div>
            )}
            {rule.bad_answers && (
              <div>✗ Bad examples defined</div>
            )}
          </div>
        )}
      </span>
    );
  }

  // Handle bonus rules
  if (rule.rule_type === RuleType.BONUS) {
    if (!rule.condition) {
      return <span className="text-muted-foreground italic">No condition specified</span>;
    }

    const bonusPoints = rule.bonus_points || 0;
    let conditionSummary: string;

    if ('question_id' in rule.condition) {
      conditionSummary = summarizeFilterCondition(rule.condition, questionLabel);
    } else {
      conditionSummary = summarizeCompoundCondition(rule.condition);
    }

    return (
      <span>
        Award <strong>+{bonusPoints}</strong> bonus point{bonusPoints !== 1 ? 's' : ''} if {conditionSummary}
      </span>
    );
  }

  return <span className="text-muted-foreground italic">Unknown rule type</span>;
}

// Helper component for displaying rule summaries in lists
export function RuleSummaryBadge({ rule, questionLabel, className }: RuleSummaryProps & { className?: string }) {
  return (
    <div className={`text-xs text-muted-foreground bg-muted/50 rounded px-2 py-1 ${className || ''}`}>
      <RuleSummary rule={rule} questionLabel={questionLabel} />
    </div>
  );
}

// Helper component for compact rule display
export function CompactRuleSummary({ rule, questionLabel }: RuleSummaryProps) {
  if (!rule.rule_type || !rule.condition) {
    return <span className="text-muted-foreground italic text-xs">Incomplete</span>;
  }

  if (rule.rule_type === RuleType.SCORING) {
    const weight = rule.weight || 1;
    return (
      <span className="text-xs">
        <span className="font-medium">{weight}pt</span> if condition met
      </span>
    );
  }

  if (rule.rule_type === RuleType.BONUS) {
    const bonusPoints = rule.bonus_points || 0;
    return (
      <span className="text-xs">
        <span className="font-medium text-green-600">+{bonusPoints}pt</span> bonus
      </span>
    );
  }

  return null;
}
