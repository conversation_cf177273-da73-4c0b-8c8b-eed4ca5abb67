/**
 * Investment Thesis Types
 *
 * This file contains TypeScript types for the Investment Thesis Configuration feature.
 * Types are based on the backend models in backend/app/models/thesis.py
 */

import { Form, QuestionType } from './form';

// Enums matching backend
export enum ThesisStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  ARCHIVED = "archived"
}

export enum RuleType {
  SCORING = "scoring",
  BONUS = "bonus"
}

export enum AggregationType {
  NONE = "none",
  AVG = "avg",
  MIN = "min",
  MAX = "max",
  COUNT = "count",
  PERCENT = "percent",
  ALL_MATCH = "all_match",
  ANY_MATCH = "any_match"
}

export enum ConditionOperator {
  EQUALS = "eq",
  NOT_EQUALS = "ne",
  GREATER_THAN = "gt",
  LESS_THAN = "lt",
  GREATER_THAN_EQUALS = "gte",
  LESS_THAN_EQUALS = "lte",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains",
  STARTS_WITH = "starts_with",
  ENDS_WITH = "ends_with",
  IN = "in",
  NOT_IN = "not_in",
  BETWEEN = "between",
  NOT_BETWEEN = "not_between"
}

export enum LogicalOperator {
  AND = "and",
  OR = "or",
  NOT = "not"
}

// Condition interfaces (matching backend FilterCondition)
export interface FilterCondition {
  question_id: string;
  operator: ConditionOperator;
  value: any;
}

export interface CompoundFilter {
  operator: LogicalOperator;
  conditions: (FilterCondition | CompoundFilter)[];
}

// For backward compatibility
export type SimpleCondition = FilterCondition;
export type CompoundCondition = CompoundFilter;

// Rule interfaces (matching backend models exactly)
export interface ScoringRule {
  _id?: string;
  id?: string;
  thesis_id: string;
  rule_type: RuleType;

  // Core fields for SCORING rules
  question_id?: string; // Required for SCORING rules, optional for BONUS rules
  question_type?: QuestionType;
  weight: number; // Default 1.0, must be positive

  // Core fields for BONUS rules
  bonus_points?: number; // Required for BONUS rules

  // REQUIRED: Condition logic (backend requires this for ALL rules)
  condition: FilterCondition | CompoundFilter;

  // Repeatable section support
  section_id?: string;
  aggregation?: AggregationType;
  filter?: FilterCondition;
  value_field?: string;
  aggregate_operator?: ConditionOperator;
  aggregate_threshold?: number;

  // Metadata
  notes?: string;
  is_deleted: boolean;
  created_at?: number;
  updated_at?: number;

  // Text answer scoring (for LLM evaluation)
  good_answers?: string; // Examples of good answers for text questions
  bad_answers?: string; // Examples of bad answers for text questions

  // Legacy fields for backward compatibility (will be removed)
  expected_value?: any;
  is_repeatable?: boolean;
  aggregation_type?: AggregationType;
  aggregation_field?: string;
  compound_filter?: CompoundFilter;
  exclude_from_scoring?: boolean;
  compound_condition?: CompoundFilter; // For bonus rules (legacy)
}

export interface MatchRule {
  _id?: string;
  id?: string;
  thesis_id: string;
  name: string;
  description?: string;
  operator: "and" | "or";
  conditions: (FilterCondition | CompoundFilter)[]; // Support both simple and compound conditions
  is_deleted: boolean;
  created_at?: number;
  updated_at?: number;
}

// BonusRule is now part of ScoringRule with rule_type = "bonus"
export type BonusRule = ScoringRule & {
  rule_type: RuleType.BONUS;
  bonus_points: number;
  compound_condition: CompoundFilter;
};

// Main thesis interface (matching backend)
export interface InvestmentThesis {
  _id?: string;
  id?: string;
  org_id: string;
  name: string;
  description: string;
  form_id: string;
  status: ThesisStatus;
  is_active: boolean;
  is_deleted: boolean;
  scoring_rules: string[]; // Array of rule IDs
  match_rules: string[]; // Array of rule IDs
  created_by: string;
  created_at?: number;
  updated_at?: number;
  submitted_at?: number;
  reviewed_at?: number;
}

// Thesis with expanded rules
export interface ThesisWithRules {
  _id?: string;
  id?: string;
  org_id: string;
  name: string;
  description: string;
  form_id: string;
  status: ThesisStatus;
  is_active: boolean;
  is_deleted: boolean;
  scoring_rules: ScoringRule[];
  match_rules: MatchRule[];
  created_by: string;
  created_at?: number;
  updated_at?: number;
  submitted_at?: number;
  reviewed_at?: number;
}

// Request interfaces
export interface ThesisCreateRequest {
  name: string;
  description: string;
  form_id: string;
  status?: ThesisStatus;
  is_active?: boolean;
  scoring_rules?: Partial<ScoringRule>[];
  match_rules?: Partial<MatchRule>[];
}

export interface ScoringRuleCreateRequest {
  rule_type: RuleType.SCORING;
  question_id: string; // Required for SCORING rules
  weight: number; // Must be positive
  condition: FilterCondition | CompoundFilter; // REQUIRED: Backend validation requires this (can be either type)

  // Optional fields
  question_type?: QuestionType;
  section_id?: string;
  aggregation?: AggregationType;
  filter?: FilterCondition;
  value_field?: string;
  aggregate_operator?: ConditionOperator;
  aggregate_threshold?: number;
  notes?: string;

  // Text answer scoring (for LLM evaluation)
  good_answers?: string; // Examples of good answers for text questions
  bad_answers?: string; // Examples of bad answers for text questions

  // Legacy fields (for backward compatibility)
  expected_value?: any;
  good_answer_examples?: string[];
  bad_answer_examples?: string[];
  is_repeatable?: boolean;
  aggregation_type?: AggregationType;
  compound_filter?: CompoundCondition;
}

export interface MatchRuleCreateRequest {
  name: string;
  description?: string;
  operator: "and" | "or";
  conditions: FilterCondition[];
}

export interface BonusRuleCreateRequest {
  rule_type: RuleType.BONUS;
  bonus_points: number;
  condition: CompoundFilter; // REQUIRED: Backend validation requires this (compound for bonus rules)

  // Optional fields
  question_id?: string; // Optional for bonus rules
  weight?: number; // Should be 1.0 for bonus rules
  notes?: string;

  // Legacy field (for backward compatibility)
  compound_condition?: CompoundCondition;
}

// UI State interfaces
export interface ThesisFormState {
  thesis: Partial<InvestmentThesis>;
  selectedForm?: Form;
  scoringRules: Partial<ScoringRule>[];
  matchRules: Partial<MatchRule>[];
  bonusRules: Partial<BonusRule>[];
}

// Question scoring configuration for UI
export interface QuestionScoringConfig {
  question_id: string;
  question_label: string;
  question_type: string;
  section_title: string;
  section_id: string;
  is_repeatable: boolean;
  included: boolean;
  weight: number;
  expected_value?: any;
  good_answer_examples?: string[];
  bad_answer_examples?: string[];
  aggregation_type?: AggregationType;
  aggregation_field?: string;
}

// Utility types
export type ScoringOrBonusRule = ScoringRule | BonusRule;
export type AnyRule = ScoringRule | MatchRule | BonusRule;

// Helper functions
export function isSimpleCondition(condition: SimpleCondition | CompoundCondition): condition is SimpleCondition {
  return 'question_id' in condition;
}

export function isCompoundCondition(condition: SimpleCondition | CompoundCondition): condition is CompoundCondition {
  return 'operator' in condition && 'conditions' in condition;
}

export function isScoringRule(rule: AnyRule): rule is ScoringRule {
  return 'rule_type' in rule && rule.rule_type === RuleType.SCORING;
}

export function isBonusRule(rule: AnyRule): rule is BonusRule {
  return 'rule_type' in rule && rule.rule_type === RuleType.BONUS;
}

export function isMatchRule(rule: any): rule is MatchRule {
  return 'operator' in rule && 'conditions' in rule && !('rule_type' in rule);
}

// Display helpers
export function getOperatorDisplay(operator: ConditionOperator): string {
  const operatorMap: Record<ConditionOperator, string> = {
    [ConditionOperator.EQUALS]: "equals",
    [ConditionOperator.NOT_EQUALS]: "not equals",
    [ConditionOperator.GREATER_THAN]: "greater than",
    [ConditionOperator.LESS_THAN]: "less than",
    [ConditionOperator.GREATER_THAN_EQUALS]: "greater than or equal",
    [ConditionOperator.LESS_THAN_EQUALS]: "less than or equal",
    [ConditionOperator.IN]: "is one of",
    [ConditionOperator.NOT_IN]: "is not one of",
    [ConditionOperator.CONTAINS]: "contains",
    [ConditionOperator.NOT_CONTAINS]: "does not contain",
    [ConditionOperator.STARTS_WITH]: "starts with",
    [ConditionOperator.ENDS_WITH]: "ends with",
    [ConditionOperator.BETWEEN]: "between",
    [ConditionOperator.NOT_BETWEEN]: "not between"
  };
  return operatorMap[operator] || operator;
}

export function getAggregationDisplay(aggregation: AggregationType): string {
  const aggregationMap: Record<AggregationType, string> = {
    [AggregationType.NONE]: "None",
    [AggregationType.AVG]: "Average",
    [AggregationType.MIN]: "Minimum",
    [AggregationType.MAX]: "Maximum",
    [AggregationType.COUNT]: "Count",
    [AggregationType.PERCENT]: "Percentage",
    [AggregationType.ALL_MATCH]: "All Match",
    [AggregationType.ANY_MATCH]: "Any Match"
  };
  return aggregationMap[aggregation] || aggregation;
}
