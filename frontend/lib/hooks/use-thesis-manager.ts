"use client";

import {use<PERSON><PERSON>back, useEffect, useState} from "react";
import {useRouter} from "next/navigation";
import {toast} from "@/components/ui/use-toast";

import {
  BonusRule,
  BonusRuleCreateRequest,
  CompoundFilter,
  isBonus<PERSON>ule,
  Logical<PERSON><PERSON>ator,
  MatchRule,
  MatchRuleCreateRequest,
  QuestionScoringConfig,
  RuleType,
  ScoringRule,
  ScoringRuleCreateRequest,
  ThesisCreateRequest,
  ThesisStatus,
  ThesisWithRules,
} from "@/lib/types/thesis";
import {Form} from "@/lib/types/form";
import {ThesisAPI} from "@/lib/api/thesis-api";
import {FormAPI} from "@/lib/api/form-api";

interface UseThesisManagerProps {
  initialThesis?: ThesisWithRules;
  onSaveSuccess?: (thesis: ThesisWithRules) => void;
}

interface ThesisState {
  thesis: Partial<ThesisWithRules>;
  scoringRules: Partial<ScoringRule>[];
  matchRules: Partial<MatchRule>[];
  bonusRules: Partial<BonusRule>[];
  selectedForm?: Form;
}

// ────────────────────────────────────────────────────────────────────────────────
//  Helper: take a full ThesisWithRules and split out scoring, bonus, and match rules
// ────────────────────────────────────────────────────────────────────────────────
function normalizeThesisResponse(
  full: ThesisWithRules
): {
  thesis: Partial<ThesisWithRules>;
  scoringRules: Partial<ScoringRule>[];
  bonusRules: Partial<BonusRule>[];
  matchRules: Partial<MatchRule>[];
} {
  // Copy over the "top-level" thesis fields
  const thesisFields: Partial<ThesisWithRules> = {
    _id: full._id || full.id,
    id: full.id,
    name: full.name,
    description: full.description,
    form_id: full.form_id,
    status: full.status,
    is_active: full.is_active,
    org_id: full.org_id,
    is_deleted: full.is_deleted,
    created_by: full.created_by,
    created_at: full.created_at,
    updated_at: full.updated_at,
    submitted_at: full.submitted_at,
    reviewed_at: full.reviewed_at,
  };

  // All scoring_rules come back mixed (both SCORING and BONUS types)
  const scoringRules = (full.scoring_rules ?? [])
    .filter((r) => r.rule_type === RuleType.SCORING)
    .map((rule) => ({
      ...rule,
      _id: rule.id,
      id: undefined,
    }));

  const bonusRules = (full.scoring_rules ?? [])
    .filter(isBonusRule)
    .map((rule) => ({
      ...rule,
      _id: rule.id,
      id: undefined,
      compound_condition: rule.compound_condition || rule.condition,
      condition: rule.condition,
    }));

  // match_rules come back in an array; drop any is_deleted
  const matchRules = (full.match_rules ?? [])
    .filter((r) => !r.is_deleted)
    .map((rule) => ({
      ...rule,
      _id: rule._id || rule.id,
      id: undefined,
    }));

  return {
    thesis: thesisFields,
    scoringRules,
    bonusRules,
    matchRules,
  };
}

// ────────────────────────────────────────────────────────────────────────────────
//  The Hook
// ────────────────────────────────────────────────────────────────────────────────
export function useThesisManager({
                                   initialThesis,
                                   onSaveSuccess,
                                 }: UseThesisManagerProps = {}) {
  const router = useRouter();

  // ─── State ─────────────────────────────────────────────────────────────────────
  const [thesisState, setThesisState] = useState<ThesisState>(() => {
    if (initialThesis) {
      // Normalize the initial payload once
      const {thesis, scoringRules, bonusRules, matchRules} =
        normalizeThesisResponse(initialThesis);
        
      return {
        thesis,
        scoringRules,
        bonusRules,
        matchRules,
        selectedForm: undefined, // We'll load this in useEffect
      };
    }

    return {
      thesis: {},
      scoringRules: [],
      bonusRules: [],
      matchRules: [],
      selectedForm: undefined,
    };
  });

  const [questionConfigs, setQuestionConfigs] = useState<
    QuestionScoringConfig[]
  >([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // ─── Helpers ────────────────────────────────────────────────────────────────────

  // Whenever the backend returns a fresh ThesisWithRules, call this to
  // update all relevant slices of local state.
  const setThesisFromResponse = useCallback(
    (updated: ThesisWithRules) => {
      const {thesis, scoringRules, bonusRules, matchRules} =
        normalizeThesisResponse(updated);

      setThesisState((prev) => ({
        ...prev,
        thesis,
        scoringRules,
        bonusRules,
        matchRules,
      }));
    },
    []
  );

  // Rebuild the questionConfigs array whenever the form loads or scoringRules change
  const generateQuestionConfigs = useCallback(
    (form: Form, scoringRules: Partial<ScoringRule>[]) => {
      const configs: QuestionScoringConfig[] = [];

      console.log('🔴 CRITICAL: generateQuestionConfigs starting...');

      form.sections.forEach((section, sectionIndex) => {
        console.log(`🔴 CRITICAL: Processing section ${sectionIndex}: ${section.title}`);
        
        section.questions.forEach((question, questionIndex) => {
          const qid = question._id || question.id || "";
          console.log(`🔴 CRITICAL: Question ${questionIndex} "${question.label}" - Raw IDs: _id="${question._id}", id="${question.id}", final qid="${qid}"`);
          
          const existingRule = scoringRules.find(
            (r) => r.question_id === qid
          );

          const config = {
            question_id: qid,
            question_label: question.label,
            question_type: question.type,
            section_title: section.title,
            section_id: section._id || section.id || "",
            is_repeatable: section.repeatable,
            included: Boolean(existingRule),
            weight: existingRule?.weight ?? 1,
            expected_value: existingRule?.expected_value,
            aggregation_type: existingRule?.aggregation_type,
          };
          
          console.log(`🔴 CRITICAL: Generated config for question "${question.label}":`, {
            question_id: config.question_id,
            question_label: config.question_label,
            question_type: config.question_type
          });
          
          configs.push(config);
        });
      });

      console.log('🔴 CRITICAL: Final questionConfigs:', configs.map(c => ({
        id: c.question_id,
        label: c.question_label,
        type: c.question_type
      })));

      setQuestionConfigs(configs);
    },
    []
  );

  // ─── Effects ───────────────────────────────────────────────────────────────────

  // Whenever we set a form_id on the thesis, load the form details exactly once
  useEffect(() => {
    const fid = thesisState.thesis.form_id as string | undefined;
    if (fid && !thesisState.selectedForm) {
      loadFormDetails(fid);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [thesisState.thesis.form_id]);

  // ─── API Calls / Actions ────────────────────────────────────────────────────────

  // 1) Load the form definition (sections + questions), then generate questionConfigs
  const loadFormDetails = useCallback(async (formId: string) => {
    try {
      setIsLoading(true);
      console.log('🚨 CRITICAL: Loading form with ID:', formId);
      const form = await FormAPI.getFormWithDetails(formId);
      
      console.log('🚨 CRITICAL: Form loaded:', {
        formId: form._id || form.id,
        formName: form.name,
        sectionsCount: form.sections?.length,
        totalQuestions: form.sections?.reduce((total, section) => total + (section.questions?.length || 0), 0)
      });
      
      // Log all question IDs
      form.sections?.forEach((section, sIdx) => {
        console.log(`🚨 CRITICAL: Section ${sIdx}: ${section.title}`);
        section.questions?.forEach((question, qIdx) => {
          console.log(`🚨 CRITICAL: Question ${qIdx}: "${question.label}" ID: "${question._id || question.id}" TYPE: ${question.type}`);
        });
      });
      
      setThesisState((prev) => ({...prev, selectedForm: form}));
      generateQuestionConfigs(form, thesisState.scoringRules);
    } catch (err) {
      console.error("Error loading form details:", err);
      toast({
        title: "Error loading form",
        description: "Failed to load form details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
    // We omit thesisState.scoringRules from deps to avoid infinite loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 2) updateThesis: patch the local "thesis" object (name, desc, etc.)
  const updateThesis = useCallback((updates: Partial<ThesisWithRules>) => {
    setThesisState((prev) => ({
      ...prev,
      thesis: {...prev.thesis, ...updates},
    }));
  }, []);

  // 3) selectForm: change form_id and immediately load details
  const selectForm = useCallback(
    async (formId: string) => {
      updateThesis({form_id: formId});
      await loadFormDetails(formId);
    },
    [updateThesis, loadFormDetails]
  );

  // 4) updateQuestionConfig: (UI-only) flip "included" or adjust "weight" in questionConfigs
  const updateQuestionConfig = useCallback(
    (questionId: string, config: Partial<QuestionScoringConfig>) => {
      setQuestionConfigs((prev) =>
        prev.map((q) => (q.question_id === questionId ? {...q, ...config} : q))
      );
      // Actual create/update/delete of scoringRules happens in createScoringRule/updateScoringRule/deleteScoringRule
    },
    []
  );

  // ─── Match Rule CRUD ────────────────────────────────────────────────────────────

  const createMatchRule = useCallback(
    async (rule: Partial<MatchRule>) => {
      if (!rule.name) {
        toast({
          title: "Missing Name",
          description: "A name is required for all match rules.",
          variant: "destructive",
        });
        return;
      }

      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before adding match rules.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        const updated = await ThesisAPI.createMatchRule(thesisId, {
          name: rule.name,
          description: rule.description ?? "",
          operator: rule.operator ?? "and",
          conditions: rule.conditions ?? [],
        } as MatchRuleCreateRequest);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Match rule created successfully."});
      } catch (err: any) {
        console.error("Error creating match rule:", err);
        toast({
          title: "Error creating match rule",
          description: err.message ?? "Failed to create match rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  const updateMatchRule = useCallback(
    async (ruleId: string, updates: Partial<MatchRule>) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before updating match rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot update rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        const updated = await ThesisAPI.updateMatchRule(ruleId, updates as MatchRuleCreateRequest);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Match rule updated successfully."});
      } catch (err: any) {
        console.error("Error updating match rule:", err);
        toast({
          title: "Error updating match rule",
          description: err.message ?? "Failed to update match rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  const deleteMatchRule = useCallback(
    async (ruleId: string) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before deleting match rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot delete rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        await ThesisAPI.deleteMatchRule(ruleId);
        // Fetch fresh thesis to keep state consistent
        const updated = await ThesisAPI.getThesis(thesisId);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Match rule deleted successfully."});
      } catch (err: any) {
        console.error("Error deleting match rule:", err);
        toast({
          title: "Error deleting match rule",
          description: err.message ?? "Failed to delete match rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  // Legacy alias for backward compatibility:
  const addMatchRule = useCallback(
    (rule: Partial<MatchRule>) => createMatchRule(rule),
    [createMatchRule]
  );

  // Legacy remove function for backward compatibility
  const removeMatchRule = useCallback(
    (index: number) => {
      // For legacy compatibility, we need to handle index-based removal
      const rule = thesisState.matchRules[index];
      if (rule && (rule._id || rule.id)) {
        deleteMatchRule(rule._id || rule.id!);
      } else {
        console.warn('Cannot remove match rule without valid ID:', rule);
        toast({
          title: "Cannot remove rule",
          description: "Rule must be saved before it can be removed.",
          variant: "destructive",
        });
      }
    },
    [thesisState.matchRules, deleteMatchRule]
  );

  // ─── Bonus Rule CRUD ────────────────────────────────────────────────────────────

  const createBonusRule = useCallback(
    async (rule: Partial<BonusRule>) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before adding bonus rules.",
          variant: "destructive",
        });
        return;
      }

      // Ensure we have a CompoundFilter for bonus rules
      let condition: CompoundFilter;
      if (rule.compound_condition) {
        condition = rule.compound_condition;
      } else if (rule.condition && 'conditions' in rule.condition) {
        // It's already a CompoundFilter
        condition = rule.condition as CompoundFilter;
      } else {
        // Default empty compound condition
        condition = {
          operator: LogicalOperator.AND,
          conditions: [],
        };
      }

      const defaultRule: Partial<BonusRuleCreateRequest> = {
        rule_type: RuleType.BONUS,
        bonus_points: rule.bonus_points ?? 1,
        condition,
        notes: rule.notes ?? "",
      };

      try {
        setIsSaving(true);
        const updated = await ThesisAPI.createBonusRule(thesisId, defaultRule as BonusRuleCreateRequest);
        setThesisFromResponse(updated);
        toast({
          title: "Success",
          description: "Bonus rule created successfully. Configure the conditions and points.",
        });
      } catch (err: any) {
        console.error("Error creating bonus rule:", err);
        toast({
          title: "Error creating bonus rule",
          description: err.message ?? "Failed to create bonus rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  const updateBonusRule = useCallback(
    async (ruleId: string, updates: Partial<BonusRule>) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before updating bonus rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot update rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      // Ensure we have a CompoundFilter for bonus rules
      const apiUpdates: Partial<BonusRuleCreateRequest> = {
        rule_type: updates.rule_type,
        bonus_points: updates.bonus_points,
        notes: updates.notes,
        question_id: updates.question_id,
        weight: updates.weight,
      };

      // Handle condition properly
      if (updates.compound_condition) {
        apiUpdates.condition = updates.compound_condition;
      } else if (updates.condition && 'conditions' in updates.condition) {
        // It's already a CompoundFilter
        apiUpdates.condition = updates.condition as CompoundFilter;
      } else if (updates.condition) {
        // It's a FilterCondition, wrap it in a CompoundFilter
        apiUpdates.condition = {
          operator: LogicalOperator.AND,
          conditions: [updates.condition],
        };
      }

      try {
        setIsSaving(true);
        const updated = await ThesisAPI.updateBonusRule(ruleId, apiUpdates as BonusRuleCreateRequest);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Bonus rule updated successfully."});
      } catch (err: any) {
        console.error("Error updating bonus rule:", err);
        toast({
          title: "Error updating bonus rule",
          description: err.message ?? "Failed to update bonus rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  const deleteBonusRule = useCallback(
    async (ruleId: string) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before deleting bonus rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot delete rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        await ThesisAPI.deleteScoringRule(ruleId);
        const updated = await ThesisAPI.getThesis(thesisId);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Bonus rule deleted successfully."});
      } catch (err: any) {
        console.error("Error deleting bonus rule:", err);
        toast({
          title: "Error deleting bonus rule",
          description: err.message ?? "Failed to delete bonus rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  // Legacy alias for backward compatibility:
  const addBonusRule = useCallback(
    (rule: Partial<BonusRule>) => createBonusRule(rule),
    [createBonusRule]
  );

  // Legacy remove function for backward compatibility
  const removeBonusRule = useCallback(
    (index: number) => {
      // For legacy compatibility, we need to handle index-based removal
      const rule = thesisState.bonusRules[index];
      if (rule && (rule._id || rule.id)) {
        deleteBonusRule(rule._id || rule.id!);
      } else {
        console.warn('Cannot remove bonus rule without valid ID:', rule);
        toast({
          title: "Cannot remove rule",
          description: "Rule must be saved before it can be removed.",
          variant: "destructive",
        });
      }
    },
    [thesisState.bonusRules, deleteBonusRule]
  );

  // ─── Scoring Rule CRUD ──────────────────────────────────────────────────────────

  const createScoringRule = useCallback(
    async (questionId: string, rule: Partial<ScoringRule>) => {
      if (!rule.condition) {
        toast({
          title: "Missing Condition",
          description: "A condition is required for all scoring rules.",
          variant: "destructive",
        });
        return;
      }

      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before adding scoring rules.",
          variant: "destructive",
        });
        return;
      }

      // Validate question ID
      if (!questionId || questionId.trim() === "") {
        toast({
          title: "Invalid Question",
          description: "Question ID is required for scoring rules.",
          variant: "destructive",
        });
        return;
      }

      // Prevent duplicates in local state
      const hasDuplicate = thesisState.scoringRules.some(
        (r) => r.question_id === questionId
      );
      if (hasDuplicate) {
        toast({
          title: "Duplicate Rule",
          description: "A scoring rule already exists for this question. Please edit the existing rule instead.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        const payload = {
          question_id: questionId,
          rule_type: RuleType.SCORING,
          weight: rule.weight ?? 1.0,
          condition: rule.condition,
          aggregation: rule.aggregation,
          value_field: rule.value_field,
          notes: rule.notes,
          good_answers: rule.good_answers,
          bad_answers: rule.bad_answers,
        } as ScoringRuleCreateRequest;
        
        const updated = await ThesisAPI.createScoringRule(thesisId, payload);
        setThesisFromResponse(updated);

        // Update questionConfigs so that UI immediately reflects "included = true"
        updateQuestionConfig(questionId, {included: true});

        toast({title: "Success", description: "Scoring rule created successfully."});
      } catch (err: any) {
        console.error("Error creating scoring rule:", err);
        toast({
          title: "Error creating scoring rule",
          description: err.message ?? "Failed to create scoring rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, thesisState.scoringRules, updateQuestionConfig, setThesisFromResponse]
  );

  const updateScoringRule = useCallback(
    async (ruleId: string, updates: Partial<ScoringRule>) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before updating scoring rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot update rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      try {
        setIsSaving(true);
        const updated = await ThesisAPI.updateScoringRule(ruleId, updates as ScoringRuleCreateRequest);
        setThesisFromResponse(updated);
        toast({title: "Success", description: "Scoring rule updated successfully."});
      } catch (err: any) {
        console.error("Error updating scoring rule:", err);
        toast({
          title: "Error updating scoring rule",
          description: err.message ?? "Failed to update scoring rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, setThesisFromResponse]
  );

  const deleteScoringRule = useCallback(
    async (ruleId: string) => {
      const thesisId = thesisState.thesis._id as string | undefined;
      if (!thesisId) {
        toast({
          title: "Save Thesis First",
          description: "Please save the thesis before deleting scoring rules.",
          variant: "destructive",
        });
        return;
      }
      if (!ruleId || ruleId === "temp") {
        toast({
          title: "Invalid Rule",
          description: "Cannot delete rule without valid ID.",
          variant: "destructive",
        });
        return;
      }

      // Remember question_id locally so we can update questionConfigs after deletion
      let toDeleteQuestionId: string | undefined;
      thesisState.scoringRules.forEach((r) => {
        if (r._id === ruleId || r.id === ruleId) {
          toDeleteQuestionId = r.question_id;
        }
      });

      try {
        setIsSaving(true);
        await ThesisAPI.deleteScoringRule(ruleId);
        const updated = await ThesisAPI.getThesis(thesisId);
        setThesisFromResponse(updated);

        // Reflect "included = false" in questionConfigs if we know the question ID
        if (toDeleteQuestionId) {
          updateQuestionConfig(toDeleteQuestionId, {included: false});
        }

        toast({title: "Success", description: "Scoring rule deleted successfully."});
      } catch (err: any) {
        console.error("Error deleting scoring rule:", err);
        toast({
          title: "Error deleting scoring rule",
          description: err.message ?? "Failed to delete scoring rule. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    },
    [thesisState.thesis._id, thesisState.scoringRules, setThesisFromResponse, updateQuestionConfig]
  );

  // ─── Save / Create Thesis ───────────────────────────────────────────────────────

  const saveThesis = useCallback(async () => {
    const {name, description, form_id, _id} = thesisState.thesis;

    if (!name || !description || !form_id) {
      toast({
        title: "Missing information",
        description: "Please provide a name, description, and select a form for your thesis.",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    try {
      const payload: ThesisCreateRequest = {
        name,
        description,
        form_id,
        status: thesisState.thesis.status ?? ThesisStatus.DRAFT,
        is_active: thesisState.thesis.is_active ?? true,
      };

      let saved: ThesisWithRules;
      if (_id) {
        saved = await ThesisAPI.updateThesis(_id as string, payload);
      } else {
        saved = await ThesisAPI.createThesis(payload);
      }

      setThesisFromResponse(saved);
      toast({
        title: "Success",
        description: `Thesis ${_id ? "updated" : "created"} successfully.`,
      });

      if (onSaveSuccess) {
        onSaveSuccess(saved);
      }

      // If newly created, redirect to the list page
      if (!_id) {
        router.push("/theses");
      }
    } catch (err: any) {
      console.error("Error saving thesis:", err);
      const errMsg =
        err.response?.data?.detail ?? err.message ?? "Failed to save thesis. Please try again.";
      toast({
        title: "Error saving thesis",
        description: errMsg,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [thesisState.thesis, onSaveSuccess, router, setThesisFromResponse]);

  // ─── Return Public API ──────────────────────────────────────────────────────────
  return {
    thesisState,
    questionConfigs,
    isSaving,
    isLoading,

    updateThesis,
    selectForm,
    updateQuestionConfig,

    createMatchRule,
    updateMatchRule,
    deleteMatchRule,
    addMatchRule, // legacy
    removeMatchRule, // legacy

    createBonusRule,
    updateBonusRule,
    deleteBonusRule,
    addBonusRule, // legacy
    removeBonusRule, // legacy

    createScoringRule,
    updateScoringRule,
    deleteScoringRule,

    saveThesis,
    loadFormDetails,
  };
}
